#nullable enable
using System.Text.Json;

namespace Dragonlabz.Stitching.Persistance.EFCore.Extensions;

public static class JsonExtensions
{
    /// <summary>
    /// Converts a JSON string to a JsonDocument
    /// </summary>
    /// <param name="json">JSON string</param>
    /// <returns>JsonDocument or null if input is null or empty</returns>
    public static JsonDocument? ToJsonDocument(this string? json)
    {
        if (string.IsNullOrEmpty(json))
        {
            return null;
        }

        return JsonDocument.Parse(json);
    }

    /// <summary>
    /// Converts a JsonDocument to its string representation
    /// </summary>
    /// <param name="document">JsonDocument</param>
    /// <returns>JSON string or null if input is null</returns>
    public static string? ToJsonString(this JsonDocument? document)
    {
        if (document == null)
        {
            return null;
        }

        return JsonSerializer.Serialize(document);
    }

    /// <summary>
    /// Serializes an object to a JSON string
    /// </summary>
    /// <typeparam name="T">Object type</typeparam>
    /// <param name="obj">Object to serialize</param>
    /// <returns>JSON string</returns>
    public static string ToJson<T>(this T obj)
    {
        if (obj == null)
        {
            return string.Empty;
        }

        return JsonSerializer.Serialize(obj);
    }

    /// <summary>
    /// Deserializes a JSON string to an object
    /// </summary>
    /// <typeparam name="T">Object type</typeparam>
    /// <param name="json">JSON string</param>
    /// <returns>Deserialized object or default if input is null or empty</returns>
    public static T? FromJson<T>(this string? json)
    {
        if (string.IsNullOrEmpty(json))
        {
            return default;
        }

        return JsonSerializer.Deserialize<T>(json);
    }
}
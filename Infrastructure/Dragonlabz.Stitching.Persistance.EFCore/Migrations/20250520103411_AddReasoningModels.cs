using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace Dragonlabz.Stitching.Persistance.EFCore.Migrations
{
    /// <inheritdoc />
    public partial class AddReasoningModels : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "LookupEngines",
                columns: new[] { "Id", "ContextWindowSize", "CreatedOnUtc", "EngineOptions", "EngineType", "Identifier", "Name", "Priority", "UpdatedOnUtc" },
                values: new object[,]
                {
                    { 44, 180000, new DateTime(2024, 12, 9, 11, 24, 31, 367, DateTimeKind.Utc).AddTicks(7030), "{\"ReasoningEffort\": \"medium\", \"MaxOutput\": 8192}", 1, "GPT_o1", "GPT o1-sw (24-12-17)", 0, null },
                    { 45, 115200, new DateTime(2024, 12, 9, 11, 24, 31, 367, DateTimeKind.Utc).AddTicks(7030), "{\"MaxOutput\": 8192}", 1, "GPT_o1_mini", "GPT o1-mini-sw (24-09-12)", 0, null },
                    { 46, 115200, new DateTime(2024, 12, 9, 11, 24, 31, 367, DateTimeKind.Utc).AddTicks(7030), "{\"ReasoningEffort\": \"medium\", \"MaxOutput\": 8192}", 1, "GPT_o3_mini", "GPT o3-mini-sw (25-01-31)", 0, null },
                    { 47, 180000, new DateTime(2024, 12, 9, 11, 24, 31, 367, DateTimeKind.Utc).AddTicks(7030), "{\"ReasoningEffort\": \"medium\", \"MaxOutput\": 8192}", 1, "GPT_o4_mini", "GPT o4-mini-GL (25-04-16)", 0, null },
                    { 48, 943718, new DateTime(2024, 12, 9, 11, 24, 31, 367, DateTimeKind.Utc).AddTicks(7030), "{\"MaxOutput\": 8192}", 1, "Gemini_2_5_Pro", "Gemini 2.5pro-GL (preview)", 0, null },
                    { 49, 180000, new DateTime(2024, 12, 9, 11, 24, 31, 367, DateTimeKind.Utc).AddTicks(7030), "{\"Temperature\": 0.8, \"BudgetTokens\": 1024, \"MaxOutput\": 8192}", 1, "Claude3_7_Sonnet_20250219", "Claude 3.7 Sonnet 20250219", 0, null }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "LookupEngines",
                keyColumn: "Id",
                keyValue: 44);

            migrationBuilder.DeleteData(
                table: "LookupEngines",
                keyColumn: "Id",
                keyValue: 45);

            migrationBuilder.DeleteData(
                table: "LookupEngines",
                keyColumn: "Id",
                keyValue: 46);

            migrationBuilder.DeleteData(
                table: "LookupEngines",
                keyColumn: "Id",
                keyValue: 47);

            migrationBuilder.DeleteData(
                table: "LookupEngines",
                keyColumn: "Id",
                keyValue: 48);

            migrationBuilder.DeleteData(
                table: "LookupEngines",
                keyColumn: "Id",
                keyValue: 49);
        }
    }
}

using Blazored.SessionStorage;
using System.Security.Cryptography;
using System.Text;

namespace Dragonlabz.Automation.Service.AdminPanel.Services
{
    public class AuthenticationService(ISessionStorageService sessionStorage, IConfiguration configuration)
    {
        private const string SessionKey = "AdminUser";
        private readonly string _authSalt = configuration["Authentication:Salt"]
                                            ?? throw new ArgumentNullException("No salt configured");

        public async Task<string?> GetCurrentUserAsync() => await sessionStorage.GetItemAsync<string>(SessionKey);

        public async Task<string> HashPasswordAsync(string password)
        {
            // First SHA-256 hash
            var firstHash = await Sha256HashAsync(password);

            // Add salt and hash again
            var saltedHash = await Sha256HashAsync(firstHash + _authSalt);

            return saltedHash;
        }

        private async Task<string> Sha256HashAsync(string input)
        {
            using var sha256 = SHA256.Create();
            var bytes = Encoding.UTF8.GetBytes(input);
            var hashBytes = sha256.ComputeHash(bytes);

            var builder = new StringBuilder();
            foreach (var b in hashBytes)
            {
                builder.Append(b.ToString("x2"));
            }

            return builder.ToString();
        }
    }
}

using Microsoft.AspNetCore.Components.Authorization;
using System.Security.Claims;

namespace Dragonlabz.Automation.Service.AdminPanel.Services
{
    public class CustomAuthenticationStateProvider(IHttpContextAccessor httpContextAccessor)
        : AuthenticationStateProvider
    {
        public override Task<AuthenticationState> GetAuthenticationStateAsync()
        {
            var httpContext = httpContextAccessor.HttpContext;

            // Always rely on cookie authentication - no session storage fallback
            if (httpContext?.User?.Identity?.IsAuthenticated == true)
            {
                return Task.FromResult(new AuthenticationState(httpContext.User));
            }

            // Return anonymous user
            return Task.FromResult(new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity())));
        }
    }
}

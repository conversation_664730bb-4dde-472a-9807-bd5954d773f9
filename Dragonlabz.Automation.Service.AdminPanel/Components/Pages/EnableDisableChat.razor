@page "/enable-chat"
@using Dragonlabz.Stitching.Core.Models.Users.Models
@using Dragonlabz.Stitching.Persistance.EFCore.Data
@using Microsoft.EntityFrameworkCore
@using Stitching.Persistance.EFCore.Repositories
@using Microsoft.AspNetCore.Authorization
@inject IJSRuntime JSRuntime
@inject StitchingPersistance _stitchingPersistance
@inject UsersPersistence UsersPersistence
@inject FoldersPersistence FoldersPersistence
@inject StitchingContext dbContext;
@rendermode InteractiveServer
@attribute [Authorize]


<h1>User Chat Settings</h1>
<br />

@if (Users is not null)
{
    <table class="table table-striped mt-4">
        <thead>
            <tr>
                <th>Id</th>
                <th>Name</th>
                <th>Email</th>
                <th>Chat Enabled</th>
                <th>LLM Model</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var user in Users)
            {
                <tr>
                    <td>@user.RowId</td>
                    <td>@user.FirstName @user.LastName</td>
                    <td>@user.Email</td>
                    <td>
                        <div class="form-check form-switch">
                            <button class="btn btn-@(user.IsChatEnabled ? "success" : "secondary")" @onclick="() => ToggleChatEnabled(user)">
                                @(user.IsChatEnabled ? "Enabled" : "Disabled")
                            </button>
                        </div>
                    </td>
                    <td>
                        <select @bind="TargetModels[user.Id]" @bind:after="() => HandleDropdownChange(user.Id)">
                            @foreach (var model in ModelEngines)
                            {
                                <option value="@model.Id" selected="@IsSelected(model.Id, user.Id)">@model.Name</option>
                            }
                        </select>

                        @* <button @onclick="@(() => SubmitWorkflowTransfer(workflow.Id, TargetEmails[workflow.Id], TargetDepartments[workflow.Id]))" disabled="@isTransferInitiated" class="btn btn-primary">Transfer</button> *@
                    </td>
                </tr>
            }
        </tbody>
    </table>
}
else
{
    <p>Loading...</p>
}

@code {
    public List<UserView> Users { get; set; }
    private Dictionary<int, int> TargetModels = new();
    public List<Dragonlabz.Stitching.Persistance.EFCore.Models.LookupEngine> ModelEngines { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        Users = (await UsersPersistence.GetAllUsers(string.Empty, string.Empty, 0, int.MaxValue))
            .OrderByDescending(o => !o.IsChatEnabled)
            .ThenByDescending(o => o.FirstName)
            .ToList();
        StateHasChanged();
    }

    private async Task HandleDropdownChange(int userId)
    {
        int modelId = TargetModels[userId];

        (await dbContext.Users.Where(w => w.Id == userId).SingleAsync()).GeneralChatLlmEngine = modelId;
        await dbContext.SaveChangesAsync();
    }

    private async Task ToggleChatEnabled(UserView user)
    {
        await UsersPersistence.UpdateUserChatStatus(user.Id, !user.IsChatEnabled);
        await JSRuntime.InvokeVoidAsync("location.reload");
    }

    private bool IsSelected(int modelId, int userId)
    {
        return TargetModels[userId] == modelId;
    }

    protected override async Task OnInitializedAsync()
    {
        ModelEngines = await dbContext.LookupEngines
                .Where(w => w.Priority >= 0)
                .OrderByDescending(o => o.Priority)
                .ToListAsync();

        TargetModels = (await dbContext.Users.Select(s => new {s.Id, s.GeneralChatLlmEngine})
            .ToListAsync())
            .ToDictionary(k => k.Id, v => v.GeneralChatLlmEngine);

    }

}

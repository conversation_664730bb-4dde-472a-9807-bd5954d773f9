@page "/rag-chat-settings"
@using Biz = Dragonlabz.Stitching.Core.Models.FoldersAndFiles.Models
@using Dragonlabz.Stitching.Core.Models.RagSettings.Models
@using Dragonlabz.Stitching.Persistance.EFCore.Models.Enums
@using Stitching.Persistance.EFCore.Repositories
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject IJSRuntime JSRuntime
@inject StitchingPersistance _stitchingPersistance
@inject UsersPersistence UsersPersistence
@inject FoldersPersistence FoldersPersistence
@rendermode InteractiveServer

<h3>RAG Chat Settings</h3>
@if (ChatEngines is not null && GlobalDefaultRagSettings is not null)
{

<div>
    <h2>Global RAG chat settings</h2>
    <div class="input-group mb-3">
        <div class="form-group">
            <label>Min Chunk Tokens Count: </label>
            <input type="number" class="form-control" min="1" @bind="GlobalDefaultRagSettings.MinChunkSize"/>
        </div>
         <div class="form-group">
            <label>Max Chunk Tokens Count: </label>
            <input type="number" class="form-control" min="1" @bind="GlobalDefaultRagSettings.ChunkSize"/>
        </div>
        <div class="form-group">
            <label>Chunk Result Count: </label>
            <input type="number" class="form-control" min="1" @bind="GlobalDefaultRagSettings.TopK"/>
        </div>
        <div class="form-group">
            <label>Temperature: </label>
            <input type="number" class="form-control" min="0" max="1" step="0.01" @bind="GlobalDefaultRagSettings.Temperature"/>
        </div>
        <div class="form-group" style="display: flex; flex-direction: column;">
            <label>Processing LLM: </label>
            <select @bind="GlobalDefaultRagSettings.RagChatEngine">
                @foreach (var engine in ChatEngines)
                {
                    if (engine.EngineType.Equals(EngineType.Standard))
                    {
                        <option value="@engine.Id">@engine.Name</option>
                    }
                }
            </select>
        </div>
    </div>
     <div class="textarea-group">
            <textarea class="form-control" @bind="GlobalDefaultRagSettings.SystemPromptOnlyRag" placeholder="System Prompt for RAG"></textarea>
            <textarea class="form-control" @bind="GlobalDefaultRagSettings.UserPromptOnlyRag" placeholder="User Prompt for RAG"></textarea>
    </div>
    <button class="btn btn-primary" @onclick="() => UpdateGlobalRagSettings()">Update Global Rag Settings</button>
    <br />
    <div style="width: 100%; border: 0.5px solid black; margin: 20px 0;"></div>
    </div>

}
<div style="display: flex; flex-direction: column; gap: 20px;">
    <button class="btn btn-secondary" style="width: 250px" @onclick="LoadGlobalRagSettings">Load Global Rag Settings</button>
    <div>
        <InputText @bind-Value="_email" placeholder="Enter user email" />
        <button class="btn btn-primary" @onclick="LoadUserFolders">Load Settings Per Folder</button>
        <button class="btn btn-secondary" @onclick="LoadPerUserSettings">Load Settings Per User</button>
    </div>
</div>
<br />

@if (_perUserSettings && currentUserSettings is not null)
{
    <div>
        <div class="input-group mb-3">
            <div class="form-group">
                <label>Min Chunk Tokens Count: </label>
                <input type="number" class="form-control" min="1" @bind="currentUserSettings.MinChunkSize" />
            </div>
             <div class="form-group">
                <label>Max Chunk Tokens Count: </label>
                <input type="number" class="form-control" min="1" @bind="currentUserSettings.ChunkSize" />
            </div>
            <div class="form-group">
                <label>Chunk Result Count: </label>
                <input type="number" class="form-control" min="1" @bind="currentUserSettings.TopK" />
            </div>
            <div class="form-group">
                <label>Temperature: </label>
                <input type="number" class="form-control" min="0" max="1" step="0.01" @bind="currentUserSettings.Temperature" />
            </div>
             <div class="form-group" style="display: flex; flex-direction: column;">
                <label>Processing LLM: </label>
                <select @bind="currentUserSettings.RagChatEngine">
                @foreach (var engine in ChatEngines)
                {
                    <option value="@engine.Id">@engine.Name</option>
                }
                </select>
            </div>
        </div>
        <div class="textarea-group">
            <textarea class="form-control" @bind="currentUserSettings.SystemPrompt" placeholder="System Prompt for RAG + Outside Knowledge" @disabled="@(!_nonDisabled)"></textarea>
            <textarea class="form-control" @bind="currentUserSettings.UserPrompt" placeholder="System Prompt for RAG + Outside Knowledge" @disabled="@(!_nonDisabled)"></textarea>
        </div>
        <button class="btn btn-primary" @onclick="UpdateUserChatSettings">Update</button>
    </div>
}

@if (_userFolders?.Count > 0)
{
    <table class="table">
        <thead class="thead-dark">
            <tr>
                <th>Folder ID</th>
                <th>Folder Name</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var folder in _userFolders)
            {
                <tr @onclick="() => SelectFolder(folder)" style="cursor:pointer;">
                    <td>@folder.Id</td>
                    <td>@folder.Name</td>
                </tr>
                @if (_selectedFolder?.Id == folder.Id && restrictedFolderSettings is not null && unrestrictedFolderSettings is not null)
                {
                    <div>
                        <div class="input-group mb-3">
                             <div class="form-group">
                                <label>Min Chunk Tokens Count: </label>
                                <input type="number" class="form-control" min="1" @bind="restrictedFolderSettings.MinChunkSize" />
                            </div>
                            <div class="form-group">
                                <label>Max Chunk Tokens Count: </label>
                                <input type="number" class="form-control" min="1" @bind="restrictedFolderSettings.ChunkSize" />
                            </div>
                            <div class="form-group">
                                <label>Chunk Result Count: </label>
                                <input type="number" class="form-control" min="1" @bind="restrictedFolderSettings.TopK" />
                            </div>
                            <div class="form-group">
                                <label>Temperature: </label>
                                <input type="number" class="form-control" min="0" max="1" step="0.01" @bind="restrictedFolderSettings.Temperature" />
                            </div>
                            <div class="form-group" style="display: flex; flex-direction: column;">
                                <label>Processing LLM: </label>
                                <select @bind="restrictedFolderSettings.RagChatEngine">
                                    @foreach (var engine in ChatEngines)
                                    {
                                        <option value="@engine.Id">@engine.Name</option>
                                    }
                                </select>
                            </div>


                        </div>
                        <div class="textarea-group">
                            <textarea class="form-control" @bind="unrestrictedFolderSettings.SystemPromptOnlyRag" placeholder=" "></textarea>
                            <label>System Prompt for RAG only chat</label>
                        </div>
                        <div class="textarea-group">
                            <textarea class="form-control" @bind="unrestrictedFolderSettings.UserPromptOnlyRag" placeholder=" "></textarea>
                            <label>User Prompt for RAG only chat</label>
                        </div>
                        <div class="textarea-group">
                            <textarea class="form-control" @bind="unrestrictedFolderSettings.SystemPrompt" placeholder=" " @disabled="@(!_nonDisabled)"></textarea>
                            <label>System Prompt for RAG + Outside Knowledge</label>
                        </div>
                        <div class="textarea-group">
                            <textarea class="form-control" @bind="unrestrictedFolderSettings.UserPrompt" placeholder=" " @disabled="@(!_nonDisabled)"></textarea>
                            <label>User Prompt for RAG + Outside Knowledge</label>
                        </div>
                        <button class="btn btn-primary" @onclick="() => UpdateFolderChatSettings(folder)">Update</button>
                    </div>
                }
            }
        </tbody>
    </table>
}

<style>
    .textarea-group {
        position: relative;
        margin-bottom: 20px;
    }

        .textarea-group textarea {
            padding-top: 20px;
        }

        .textarea-group label {
            position: absolute;
            top: 0;
            left: 5px;
            color: #999;
            pointer-events: none;
            transition: all 0.3s;
        }

        .textarea-group textarea:focus + label,
        .textarea-group textarea:not(:placeholder-shown) + label {
            top: -20px;
            left: 0;
            font-size: 12px;
            color: #666;
        }

        /* Additional styling for disabled textarea */
        .textarea-group textarea:disabled {
            background-color: #f0f0f0;
        }
</style>

<style>
    .textarea-group {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 15px; /* Control spacing between textareas */
    }

    .textarea-group > textarea {
        flex: 1 1 calc(50% - 15px); /* Adapt width while maintaining space */
        min-height: 200px;
    }

    .input-group {
        display: flex;
        gap: 15px; /* Control spacing between inputs */
    }
</style>

@code {
    private string _email;
    private int _userId;

    private List<Biz.Folder> _userFolders;
    private Biz.Folder _selectedFolder;

    private ChatSettings? restrictedFolderSettings;
    private ChatSettings? unrestrictedFolderSettings;

    private ChatSettings? currentUserSettings;

    private ChatSettings? GlobalDefaultRagSettings;

    private int ChunkTokensCount = 1024;
    private int ChunkResultCount = 16;

    private bool _nonDisabled = true; // Mock condition
    private bool _perUserSettings = false; // Mock condition

    public List<Dragonlabz.Stitching.Persistance.EFCore.Models.LookupEngine> ChatEngines { get; set; }

    private async Task LoadUserFolders()
    {
        _perUserSettings = false;
        _userId = (await UsersPersistence.GetUserByEmail(_email)).Id;
        _userFolders = await FoldersPersistence.GetAllFoldersWithFilesByUserId(_userId);
    }

    private async Task LoadPerUserSettings()
    {
        _userId = (await UsersPersistence.GetUserByEmail(_email)).Id;
        currentUserSettings = await FoldersPersistence.GetCurrentUserSettings(_userId);
        _perUserSettings = true;
        StateHasChanged();
    }

    private async Task SelectFolder(Biz.Folder folder)
    {
        (restrictedFolderSettings, unrestrictedFolderSettings) = await FoldersPersistence.GetCurrentFolderSettings(_userId, folder.Id);
        _selectedFolder = _selectedFolder == folder ? null : folder;
        StateHasChanged();
    }

    private async Task LoadGlobalRagSettings()
    {
        GlobalDefaultRagSettings = await FoldersPersistence.GetGlobalRagSettings();
        StateHasChanged();
    }

    private async Task UpdateFolderChatSettings(Biz.Folder folder)
    {
        await FoldersPersistence.UpsertCurrentFolderSettings(_userId, folder.Id, restrictedFolderSettings, unrestrictedFolderSettings);

        await JSRuntime.InvokeVoidAsync("alert", "Folder chat settings updated!");
        StateHasChanged();
    }

    private async Task UpdateGlobalRagSettings()
    {
        await FoldersPersistence.UpsertGlobalRagSettings(GlobalDefaultRagSettings);

        await JSRuntime.InvokeVoidAsync("alert", "Global RAG chat settings updated!");
        StateHasChanged();
    }

    private async Task UpdateUserChatSettings()
    {
        await FoldersPersistence.UpsertCurrentUserSettings(_userId, currentUserSettings);

        await JSRuntime.InvokeVoidAsync("alert", "User chat settings updated!");
        StateHasChanged();
    }


    private bool IsSelected(int modelId, int engineId)
    {
        return modelId == engineId;
    }

    // <EMAIL>
    protected override async Task OnInitializedAsync()
    {
        ChatEngines = await _stitchingPersistance.GetLookupEngines();
    }
}

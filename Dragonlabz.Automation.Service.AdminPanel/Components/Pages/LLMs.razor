@page "/llms"
@using Dragonlabz.Stitching.Core.Models.Stitch.Models
@using Dragonlabz.Stitching.Persistance.EFCore.Data
@using Dragonlabz.Stitching.Persistance.EFCore.Extensions
@using Dragonlabz.Stitching.Persistance.EFCore.Models
@using Dragonlabz.Stitching.Persistance.EFCore.Models.Enums
@using Microsoft.AspNetCore.Authorization
@using Microsoft.EntityFrameworkCore
@inject StitchingContext DbContext
@inject IJSRuntime JSRuntime
@rendermode InteractiveServer
@attribute [Authorize]

<h3>Language Models</h3>

@if (Engines == null)
{
    <p>Loading...</p>
}
else
{
    <!-- Standard Engine Types -->
    <h4>Standard Models</h4>
    <div class="table-responsive mb-5">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Context Window Size</th>
                    <th>Engine Type</th>
                    <th>Options</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var engine in Engines.Where(e => e.EngineType == EngineType.Standard))
                {
                    <tr>
                        <td>@engine.Name</td>
                        <td>@engine.ContextWindowSize</td>
                        <td>@engine.EngineType</td>
                        <td>
                            <button class="btn btn-primary" @onclick="() => OpenEngineOptionsModal(engine)">Edit Options</button>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>

    <!-- Reasoning Engine Types -->
    <h4>Reasoning Models</h4>
    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Context Window Size</th>
                    <th>Engine Type</th>
                    <th>Options</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var engine in Engines.Where(e => e.EngineType == EngineType.Reasoning))
                {
                    <tr>
                        <td>@engine.Name</td>
                        <td>@engine.ContextWindowSize</td>
                        <td>@engine.EngineType</td>
                        <td>
                            <button class="btn btn-primary" @onclick="() => OpenEngineOptionsModal(engine)">Edit Options</button>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
}

@if (showModal && selectedEngine != null)
{
    <div class="modal show" tabindex="-1" style="display:block; background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Options - @selectedEngine.Name</h5>
                    <button type="button" class="btn-close" @onclick="CloseModal"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="mb-3">
                            <label for="contextWindowSize" class="form-label">Context Window Size</label>
                            <input type="number" class="form-control" id="contextWindowSize" @bind="editContextWindowSize">
                        </div>
                        <!-- Always display Max Output Tokens for all engine types -->
                        <div class="mb-3">
                            <label for="maxOutput" class="form-label">Max Output Tokens</label>
                            <input type="number" class="form-control" id="maxOutput" @bind="editMaxOutput">
                        </div>

                        <!-- Temperature field - show if not null -->
                        @if (editTemperatureEnabled)
                        {
                            <div class="mb-3">
                                <label for="temperature" class="form-label">Temperature</label>
                                <input type="number" step="0.1" min="0" max="1" class="form-control" id="temperature" @bind="editTemperature">
                            </div>
                        }

                        <!-- Reasoning Effort field - show if not null -->
                        @if (editReasoningEffortEnabled)
                        {
                            <div class="mb-3">
                                <label for="reasoningEffort" class="form-label">Reasoning Effort</label>
                                <select class="form-select" id="reasoningEffort" @bind="editReasoningEffort">
                                    @foreach (var option in reasoningEffortOptions)
                                    {
                                        <option value="@option">@char.ToUpper(option[0])@option.Substring(1)</option>
                                    }
                                </select>
                            </div>
                        }

                        <!-- Budget Tokens field - show if not null -->
                        @if (editBudgetTokensEnabled)
                        {
                            <div class="mb-3">
                                <label for="budgetTokens" class="form-label">Budget Tokens (must be 0 or ≥@minBudgetTokens)</label>
                                <input type="number" min="0" step="1" class="form-control" id="budgetTokens" @bind="editBudgetTokens">
                                @if (editBudgetTokens > 0 && editBudgetTokens < minBudgetTokens)
                                {
                                    <div class="text-danger">@errorBudgetTokens</div>
                                }
                            </div>
                        }
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="CloseModal">Close</button>
                    <button type="button" class="btn btn-primary" @onclick="SaveEngineOptions">Save changes</button>
                </div>
            </div>
        </div>
    </div>
}

@code {
    private List<LookupEngine> Engines { get; set; }
    private LookupEngine selectedEngine;
    private bool showModal = false;

    // Constants
    private readonly int minBudgetTokens = 1024;
    private readonly string defaultReasoningEffort = "medium";
    private readonly string errorBudgetTokens = "Budget Tokens must be either 0 or at least 1024";
    private readonly string successMessage = "Engine options updated successfully!";
    private readonly string[] reasoningEffortOptions = new[] { "low", "medium", "high" };

    // Edit form fields
    private int editContextWindowSize;
    private float editTemperature;
    private int editMaxOutput;
    private int editBudgetTokens;
    private string editReasoningEffort;

    // Field visibility flags
    private bool editTemperatureEnabled;
    private bool editBudgetTokensEnabled;
    private bool editReasoningEffortEnabled;

    protected override async Task OnInitializedAsync()
    {
        await LoadEngines();
    }

    private async Task LoadEngines()
    {
        Engines = await DbContext.LookupEngines
            .OrderByDescending(e => e.Id)
            .ToListAsync();
    }

    private void OpenEngineOptionsModal(LookupEngine engine)
    {
        selectedEngine = engine;

        // Set default values
        editContextWindowSize = engine.ContextWindowSize;

        // Reset all field values and visibility flags
        editTemperature = 0;
        editBudgetTokens = 0;
        editReasoningEffort = null;

        editTemperatureEnabled = false;
        editBudgetTokensEnabled = false;
        editReasoningEffortEnabled = false;

        // Parse engine options
        var options = engine.EngineOptions.FromJson<EngineOptions>();

        // Common fields
        editMaxOutput = options.MaxOutput;

        // Set values and visibility flags for each field based on whether it's null
        if (options.Temperature.HasValue)
        {
            editTemperature = options.Temperature.Value;
            editTemperatureEnabled = true;
        }

        if (options.BudgetTokens.HasValue)
        {
            editBudgetTokens = options.BudgetTokens.Value;
            editBudgetTokensEnabled = true;
        }

        if (!string.IsNullOrEmpty(options.ReasoningEffort))
        {
            editReasoningEffort = options.ReasoningEffort;
            editReasoningEffortEnabled = true;
        }

        showModal = true;
    }

    private void CloseModal()
    {
        showModal = false;
        selectedEngine = null;
    }

    private async Task SaveEngineOptions()
    {
        if (selectedEngine == null) return;

        // Validate BudgetTokens if enabled - must be either 0 or at least minBudgetTokens
        if (editBudgetTokensEnabled && editBudgetTokens > 0 && editBudgetTokens < minBudgetTokens)
        {
            await JSRuntime.InvokeVoidAsync("alert", errorBudgetTokens);
            return;
        }

        // Update engine context window size
        selectedEngine.ContextWindowSize = editContextWindowSize;

        // Create options object with common fields
        var options = new EngineOptions
        {
            MaxOutput = editMaxOutput
        };

        // Add nullable fields only if they are enabled
        if (editTemperatureEnabled)
        {
            options.Temperature = editTemperature;
        }

        if (editBudgetTokensEnabled)
        {
            options.BudgetTokens = editBudgetTokens;
        }

        if (editReasoningEffortEnabled)
        {
            options.ReasoningEffort = editReasoningEffort;
        }

        // Convert options to JSON string
        selectedEngine.EngineOptions = options.ToJson();

        // Save changes to databaseч
        DbContext.LookupEngines.Update(selectedEngine);
        await DbContext.SaveChangesAsync();

        await JSRuntime.InvokeVoidAsync("alert", successMessage);
        await LoadEngines(); // Reload engines list to reflect changes
        showModal = false;
    }
}

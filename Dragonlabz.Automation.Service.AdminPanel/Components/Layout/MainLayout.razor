@inherits LayoutComponentBase
@using Microsoft.AspNetCore.Components.Authorization
@inject NavigationManager NavigationManager

<div class="page">
    <div class="sidebar">
        <NavMenu />
    </div>

    <main>
        <div class="top-row px-4">
            <AuthorizeView>
                <Authorized>
                    <span>Welcome, @context.User.Identity?.Name!</span>
                    <button class="btn btn-link" @onclick="Logout">Logout</button>
                </Authorized>
            </AuthorizeView>
        </div>

        <article class="content px-4">
            @Body
        </article>
    </main>
</div>

@code {
    private void Logout()
    {
        NavigationManager.NavigateTo("/api/auth/logout", forceLoad: true);
    }
}

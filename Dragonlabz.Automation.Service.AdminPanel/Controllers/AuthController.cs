using Dragonlabz.Stitching.Persistance.EFCore.Repositories;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using AuthenticationService = Dragonlabz.Automation.Service.AdminPanel.Services.AuthenticationService;

namespace Dragonlabz.Automation.Service.AdminPanel.Controllers
{
    [Controller]
    [Route("api/[controller]")]
    public class AuthController(IConfiguration configuration,
        UsersPersistence usersPersistence,
        AuthenticationService authenticationService) : Controller
    {
        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<IActionResult> Login([FromForm] string username, [FromForm] string password, [FromForm] string? returnUrl)
        {
            // Get admin users from configuration
            var adminUsers = configuration["Authentication:AdminUsers"] ??
                             throw new ArgumentNullException("No admin users configured");

            var allAdmins = adminUsers.Split(',').Select(u => u.Trim()).ToList();

            if (allAdmins.Contains(username))
            {
                password = await authenticationService.HashPasswordAsync(password);
                var user = await usersPersistence.GetUserByEmailAndPassword(username, password);
                if (user is null)
                {
                    return Redirect("/login?error=Invalid username or password");
                }
                var claims = new List<Claim> {new(ClaimTypes.Name, username), new(ClaimTypes.Role, "Admin")};

                var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
                var authProperties = new AuthenticationProperties {IsPersistent = true, ExpiresUtc = DateTimeOffset.UtcNow.AddMinutes(30)};

                await HttpContext.SignInAsync(
                    CookieAuthenticationDefaults.AuthenticationScheme,
                    new ClaimsPrincipal(claimsIdentity),
                    authProperties);

                // Redirect to return URL or home
                if (!string.IsNullOrEmpty(returnUrl) && Url.IsLocalUrl(returnUrl))
                {
                    return Redirect(returnUrl);
                }

                return Redirect("/");
            }


            // Redirect back to login with error
            return Redirect("/login?error=Invalid username or password");
        }

        [HttpPost("logout")]
        [HttpGet("logout")]
        public async Task<IActionResult> Logout()
        {
            await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
            return Redirect("/login");
        }
    }
}

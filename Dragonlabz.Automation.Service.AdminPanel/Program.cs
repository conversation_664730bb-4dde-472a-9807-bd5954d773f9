using Blazored.SessionStorage;
using Dragonlabz.Automation.Service.AdminPanel.Components;
using Dragonlabz.Automation.Service.AdminPanel.Extensions;
using Dragonlabz.Automation.Service.AdminPanel.Services;
using Dragonlabz.Service.Mappers;
using Dragonlabz.Service.Services;
using Dragonlabz.Service.Services.Interfaces;
using Dragonlabz.Stitching.Core.Models.Emailing;
using Dragonlabz.Stitching.Core.Models.System;
using Dragonlabz.Stitching.Persistance.EFCore.Data;
using Dragonlabz.Stitching.Persistance.EFCore.Repositories;
using Dragonlabz.Stitching.Persistance.EFCore.Repositories.Interfaces;
using Dragonlabz.Stitching.SendgridEmailing;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Dragonlabz.Automation.Service.AdminPanel;

public class Program
{
    public static void Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);

        // Add services to the container.
        builder.Services
            .AddRazorComponents()
            .AddInteractiveServerComponents();

        builder.Services.AddCors();
        builder.Services.AddHttpContextAccessor();
        builder.Services.AddControllers();
        builder.Services.AddEndpointsApiExplorer();

        var apiVersionBuilder = builder.Services.AddApiVersioning(options =>
        {
            options.AssumeDefaultVersionWhenUnspecified = true;
            options.DefaultApiVersion = new ApiVersion(1, 0);
        });

        var connectionString = builder.Configuration.GetConnectionString("StitchingContext")
                               ?? throw new ArgumentNullException("StitchingContext connection string is null");

        builder.Services.AddDbContext<StitchingContext>(options =>
        {
            options.UseSqlServer(connectionString, providerOptions => providerOptions.EnableRetryOnFailure(
                3,
                TimeSpan.FromSeconds(15),
                null));
        });

        // Configure the default HttpClient here
        builder.Services
            .AddHttpClient()
            .AddScoped<StitchingContext>()
            .AddScoped<StitchingPersistance>()
            .AddScoped<UsersPersistence>()
            .AddScoped<FoldersPersistence>()
            .AddScoped<CostsRepository>()
            .AddScoped<IPromptDetailPersistence, PromptDetailsPersistence>()
            .AddScoped<IPromptDetailsService, PromptDetailsService>()
            .AddScoped<IUserTutorialService, UserTutorialService>()
            .AddScoped<IUserTutorialPersistence, UserTutorialPersistence>()
            .AddScoped<IStorage, AzureBlobFileUpload>()
            .AddScoped<IClientPersistence, ClientPersistence>()
            .AddScoped<SystemPersistence>()
            .AddScoped<IEmailer, SendgridEmailer>()
            .AddScoped<ILegalDepartmentTagPersistence, LegalDepartmentTagPersistence>()
            .AddScoped<ILegalDepartmentTagService, LegalDepartmentTagService>()
            .AddScoped<ILookupEnginePersistence, LookupEnginePersistence>();

        // Add authentication services
        builder.Services.AddBlazoredSessionStorage();
        builder.Services.AddAuthentication(options =>
        {
            options.DefaultScheme = "Cookies";
            options.DefaultChallengeScheme = "Cookies";
        })
        .AddCookie("Cookies", options =>
        {
            options.LoginPath = "/login";
            options.LogoutPath = "/logout";
            options.ExpireTimeSpan = TimeSpan.FromMinutes(30);
            options.SlidingExpiration = true;
            options.Cookie.HttpOnly = true;
            options.Cookie.SecurePolicy = CookieSecurePolicy.SameAsRequest;
            options.Cookie.SameSite = SameSiteMode.Lax;
            options.Cookie.Name = "AuthCookie";
        });
        builder.Services.AddAuthorizationCore();

        // Register authentication state provider
        builder.Services.AddScoped<CustomAuthenticationStateProvider>();
        builder.Services.AddScoped<AuthenticationStateProvider>(provider => provider.GetRequiredService<CustomAuthenticationStateProvider>());
        
        // Register authentication service (still needed for password hashing)
        builder.Services.AddScoped<AuthenticationService>();


        var app = builder.Build();

        app.ApplyDatabaseMigrations();

        // Configure the HTTP request pipeline.
        if (!app.Environment.IsDevelopment())
        {
            app.UseExceptionHandler("/errors");
            app.UseHsts();
        }

        app.UseHttpsRedirection();

        app.UseStaticFiles();
        app.UseRouting();
        
        app.UseAuthentication();
        app.UseAuthorization();
        
        app.UseAntiforgery();

        app.MapRazorComponents<App>()
            .AddInteractiveServerRenderMode();

        app.MapControllers();

        MapperConfig.ConfigureMappers();

        app.Run();
    }
}

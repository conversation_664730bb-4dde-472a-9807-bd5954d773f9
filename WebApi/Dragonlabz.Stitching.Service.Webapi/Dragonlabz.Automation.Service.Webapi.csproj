<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AspNetCore.HealthChecks.ApplicationStatus" Version="8.0.0"/>
        <PackageReference Include="AspNetCore.HealthChecks.SqlServer" Version="8.0.0"/>
        <PackageReference Include="AspNetCore.HealthChecks.System" Version="8.0.0"/>
        <PackageReference Include="AspNetCore.HealthChecks.UI" Version="8.0.0"/>
        <PackageReference Include="AspNetCore.HealthChecks.UI.Core" Version="8.0.0"/>
        <PackageReference Include="AutoBogus" Version="2.13.1"/>
        <PackageReference Include="Azure.AI.OpenAI" Version="2.2.0-beta.4" />
        <PackageReference Include="Azure.Core" Version="1.46.1" />
        <PackageReference Include="Azure.Storage.Queues" Version="12.17.1"/>
        <PackageReference Include="BetterStack.Logs.Serilog" Version="1.1.0" />
        <PackageReference Include="Google.Apis.Auth" Version="1.69.0" />
        <PackageReference Include="HarfBuzzSharp.NativeAssets.Linux" Version="7.3.0.2"/>
        <PackageReference Include="Markdig" Version="0.35.0"/>
        <PackageReference Include="Microsoft.AspNetCore.Authentication.Google" Version="8.0.1"/>
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.1"/>
        <PackageReference Include="Microsoft.AspNetCore.Authentication.MicrosoftAccount" Version="8.0.1"/>
        <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.1"/>
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.1.0"/>
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer" Version="5.1.0"/>
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.1"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.5">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.5"/>
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.5"/>
        <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="9.0.5"/>
        <PackageReference Include="Microsoft.Identity.Web" Version="2.17.0"/>
        <PackageReference Include="Microsoft.KernelMemory.Core" Version="0.30.240227.1"/>
        <PackageReference Include="morelinq" Version="4.1.0"/>
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3"/>
        <PackageReference Include="Serilog" Version="4.3.0" />
        <PackageReference Include="Serilog.AspNetCore" Version="9.0.0"/>
        <PackageReference Include="Serilog.Enrichers.Environment" Version="2.3.0"/>
        <PackageReference Include="Serilog.Enrichers.Process" Version="2.0.2"/>
        <PackageReference Include="Serilog.Enrichers.Thread" Version="3.1.0"/>
        <PackageReference Include="Serilog.Exceptions" Version="8.4.0"/>
        <PackageReference Include="Serilog.Expressions" Version="5.0.0"/>
        <PackageReference Include="Serilog.Extensions.Logging" Version="9.0.1" />
        <PackageReference Include="Serilog.Settings.Configuration" Version="9.0.0"/>
        <PackageReference Include="Serilog.Sinks.ApplicationInsights" Version="4.0.0" />
        <PackageReference Include="Serilog.Sinks.Async" Version="2.1.0" />
        <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0"/>
        <PackageReference Include="SkiaSharp.NativeAssets.Linux.NoDependencies" Version="2.88.8"/>
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0"/>
        <PackageReference Include="Syncfusion.DocIO.Net.Core" Version="27.1.52"/>
        <PackageReference Include="Syncfusion.DocIORenderer.Net.Core" Version="27.1.52"/>
        <PackageReference Include="Syncfusion.Licensing" Version="27.1.52"/>
        <PackageReference Include="Syncfusion.Pdf.Net.Core" Version="27.1.52"/>
        <PackageReference Include="Tiktoken" Version="2.2.0" />
        <PackageReference Include="OpenAI" Version="2.2.0-beta.4" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Core\Dragonlabz.Stitching.Chat\Dragonlabz.Stitching.ChatEngine.csproj"/>
        <ProjectReference Include="..\..\Core\Dragonlabz.Stitching.Core.Models\Dragonlabz.Stitching.Core.Models.csproj"/>
        <ProjectReference Include="..\..\Dragonlabz.Stitching.AzureFileUpload\Dragonlabz.Stitching.AzureFileUpload.csproj"/>
        <ProjectReference Include="..\..\Dragonlabz.Stitching.AzureOpenAiChat\Dragonlabz.Stitching.AzureOpenAiChat.csproj"/>
        <ProjectReference Include="..\..\Dragonlabz.Stitching.ChatServiceFactory\Dragonlabz.Stitching.ChatServiceFactory.csproj"/>
        <ProjectReference Include="..\..\Dragonlabz.Stitching.Chat\Dragonlabz.Stitching.OpenAiChat.csproj"/>
        <ProjectReference Include="..\..\Dragonlabz.Stitching.DocxProcessor\Dragonlabz.Stitching.DocxProcessor.csproj"/>
        <ProjectReference Include="..\..\Dragonlabz.Stitching.IpToLocation\Dragonlabz.Stitching.IpToLocation.csproj"/>
        <ProjectReference Include="..\..\Dragonlabz.Stitching.SendgridEmailing\Dragonlabz.Stitching.SendgridEmailing.csproj"/>
        <ProjectReference Include="..\..\Dragonlabz.Stitching.TogetherAiChat\Dragonlabz.Stitching.TogetherAiChat.csproj"/>
        <ProjectReference Include="..\..\Dragonlabz.Whisper\Dragonlabz.Whisper.csproj"/>
        <ProjectReference Include="..\..\Infrastructure\Dragonlabz.Stitching.Persistance.EFCore\Dragonlabz.Stitching.Persistance.EFCore.csproj"/>
        <ProjectReference Include="..\..\nugets\Dragonlabz.Platform.Webapi.Filters\Dragonlabz.Platform.Webapi.Filters.csproj"/>
        <ProjectReference Include="..\..\nugets\Dragonlabz.Platform.Webapi.Middlewares\Dragonlabz.Platform.Webapi.Middlewares.csproj"/>
        <ProjectReference Include="..\..\Services\Dragonlabz.Pinecone\Dragonlabz.Pinecone.csproj"/>
        <ProjectReference Include="..\..\Services\Dragonlabz.Service\Dragonlabz.Service.csproj"/>
        <ProjectReference Include="..\..\Services\Dragonlabz.Stitching.PromptLoader\Dragonlabz.Stitching.TemplatesManager.csproj"/>
        <ProjectReference Include="..\..\Services\Dragonlabz.Stitching.SlackLogger\Dragonlabz.Stitching.SlackLogger.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <None Update="Data\cache\1118_1.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\cache\1118_2.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\cache\1118_3.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\cache\1118_4.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\cache\1118_5.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\cache\1118_6.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\cache\1118_7.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\cache\1118_8.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\cache\3624_1.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\mock\step-2.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v1\ChainPromptGpt4.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\Gpt4PlannerPromptGeneralStructure.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\Gpt4PlannerPromptWithExamples.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\InitialStepsPrompt.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v1\QuestionsPrompt.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v1\StepsPrompt.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v2\DetailedPlan_Questions_System.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v2\DetailedPlan_Questions_User.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v2\DetailedPlan_Steps_System.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v2\DetailedPlan_Steps_User.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v2\MagicToSteps_System.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v3\DetailedPlan_Questions_System.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v3\DetailedPlan_Questions_User.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v3\DetailedPlan_Steps_System.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v3\DetailedPlan_Steps_User.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v3\MagicToSteps_System.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v3\MagicToSteps_User.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v3\StepsValidator_System.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v3\StepsValidator_User.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v4\GenerateStepTip_step1_system.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v4\GenerateStepTip_step1_user.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v4\MagicToSteps_step3_system.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v4\MagicToSteps_step3_user.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v4\Questions_step2_system.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v4\Questions_step2_user.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v4\RegenerateStep_step1_system.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v4\RegenerateStep_step1_user.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v4\RunStitch_step1_user.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v4\RunStitch_step1_system.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v4\DetailedPlan_step2_system.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v4\DetailedPlan_step1_system.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v4\Questions_step1_system.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v4\DetailedPlan_step2_user.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v4\DetailedPlan_step1_user.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v4\Questions_step1_user.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v4\MagicToSteps_step2_system.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v4\MagicToSteps_step2_user.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v4\MagicToSteps_step1_user.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Data\v4\MagicToSteps_step1_system.txt">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Data\cache\"/>
    </ItemGroup>

</Project>

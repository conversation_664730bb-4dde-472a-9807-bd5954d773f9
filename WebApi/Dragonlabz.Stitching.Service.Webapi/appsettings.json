{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft.AspNetCore": "Debug", "System.Net.Http.HttpClient": "Debug"}}, "ConnectionStrings": {"StitchingContext": "Data Source=lexity-db.database.windows.net;Initial Catalog=lexity-stage-db;Persist Security Info=False;User ID=lexity;Password=****************"}, "Slack": {"WebhookUrl": "*********************************************************************************"}, "TogetherAi": {"Token": "Bearer 559f27b5f540a5f4aba82f713c7ad88b36dad8a939c8918aa3d01dfe2073c9e9"}, "Gemini": {"Token": "AIzaSyDM2epgo11Ca7s-Mojk6EV-gfBxTH19wAI", "Url": "https://generativelanguage.googleapis.com/{{apiVersion}}/models/{{modelName}}:generateContent?key={{token}}", "ApiVersion": "v1beta"}, "OpenAi": {"ApiKey": "***************************************************", "UsdSpendingLimit": "10"}, "Anthropic": {"ApiKey": "************************************************************************************************************", "UsdSpendingLimit": "*********"}, "AzureOpenAi": {"KeyCredential": "3eafd14e88904f20856226c4c4d88695", "Endpoint": "https://stitching-open-ai.openai.azure.com/", "UsdSpendingLimit": "*********", "EmbeddingsKeyCredential": "ed29ff114e944d8097491c54677a1b06", "EmbeddingsEndpoint": "https://openai-automation.openai.azure.com/"}, "AzureOpenAiSwedenCentral": {"KeyCredential": "9ccf79985b724398ac88bfc4b60cf501", "Endpoint": "https://openai-sweden-lexity.openai.azure.com/", "UsdSpendingLimit": "*********"}, "AzureOpenAiSwitzerlandNorth": {"KeyCredential": "5d9286657041430f97e98bfe06ba5c36", "Endpoint": "https://lexity.openai.azure.com/", "UsdSpendingLimit": "*********"}, "AzureStorage": {"Container": "workflows-results", "ImageContainer": "user-avatars", "UploadsContainer": "uploads", "AccountName": "lexity", "TextChunksContainer": "text-chunks", "Key": "****************************************************************************************", "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=lexity;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"}, "AzureQueueRunStitch": {"ConnectionString": "DefaultEndpointsProtocol=https;AccountName=lexity;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "QueueName": "stitch-run"}, "AzureQueueProcessedFiles": {"ConnectionString": "DefaultEndpointsProtocol=https;AccountName=lexity;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "QueueName": "processed-files"}, "AzureQueueOcrProcessedFiles": {"ConnectionString": "DefaultEndpointsProtocol=https;AccountName=lexity;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "QueueName": "ocr-processed-files"}, "SendGrid": {"ApiKey": "*********************************************************************", "EmailSendFrom": "none", "EmailSendFromName": "none"}, "Mixpanel": {"ProjectToken": "bac1002b58e9b6e7339595c5c03faf87", "ApiSecret": "e1d0dd56cb3bfd0d544cfc441ef31576", "Enabled": false}, "Jwt": {"Key": "Yh2k7QSu4l8CZg5p6X3Pna9L0Miy4D3Bvt0JVr87UcOj69Kqw5R2Nmf4FWs03Hdx", "Issuer": "JWTAuthenticationServer", "Audience": "JWTServicePostmanClient", "Subject": "JWTServiceAccessToken", "ServerDurationMin": 43200, "ClientDurationMin": 43000}, "Authentication": {"BaseUrl": "https://stage-builder.lexity.ai", "Google": {"ClientId": "************-65idrvr2d3i5nsauuktusf74dlb9n62k.apps.googleusercontent.com", "ClientSecret": "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"}}, "Serilog": {"Using": ["Serilog.Sinks.ApplicationInsights", "BetterStack.Logs.Serilog"], "MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Debug", "System": "Debug"}}, "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"], "WriteTo": [{"Name": "ApplicationInsights", "Args": {"telemetryConverter": "Serilog.Sinks.ApplicationInsights.TelemetryConverters.TraceTelemetryConverter, Serilog.Sinks.ApplicationInsights"}}, {"Name": "File", "Args": {"path": "../../LogFiles/_logs-uifile.txt", "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level}] [{SourceContext}] [{EventId}] {Message}{NewLine}{Exception}", "rollOnFileSizeLimit": true, "fileSizeLimitBytes": 4194304, "retainedFileCountLimit": 5}}, {"Name": "BetterStack", "Args": {"sourceToken": "************************", "betterStackEndpoint": "https://s1328655.eu-nbg-2.betterstackdata.com"}}]}}
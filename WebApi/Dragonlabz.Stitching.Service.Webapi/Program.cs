using Dragonlabz.Service.Mappers;
using Dragonlabz.Stitching.Service.Webapi.ApiExtensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Versioning;
using Notifications;
using Serilog;
using Serilog.Debugging;
using Syncfusion.Licensing;

namespace Dragonlabz.Automation.Service.Webapi;

public class Program
{
    public static void Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);

        builder.Configuration.AddEnvironmentVariables();

        builder.WebHost.ConfigureKestrel(options =>
        {
            options.Limits.MaxRequestBodySize = 200 * 1024 * 1024;
        });

        try
        {
            var licenseKey = builder.Configuration["Syncfusion:LicenseKey"];
            SyncfusionLicenseProvider.RegisterLicense(licenseKey);
        }
        catch (Exception ex)
        {
            throw new Exception("Syncfusion license is invalid", ex);
        }

        builder.Services.AddHttpContextAccessor();
        builder.Services.ConfigureControllers();
        builder.ConfigureServices();

        builder.Logging.ClearProviders();
        builder.Host.UseSerilog((context, loggerConfiguration) => loggerConfiguration
            .WriteTo.Console(outputTemplate: "[{Timestamp:HH:mm:ss} {Level}] {SourceContext}{NewLine}{Message:lj}{NewLine}{Exception}{NewLine}")
            .ReadFrom.Configuration(context.Configuration));

        SelfLog.Enable(Console.Error);

        builder.Services.AddEndpointsApiExplorer();
        builder.Services.AddSwaggerGen();

        builder.Services.AddApiVersioning(options =>
        {
            options.AssumeDefaultVersionWhenUnspecified = true;
            options.DefaultApiVersion = new ApiVersion(1, 0);
            options.ReportApiVersions = true; // Add headers with api version info
            options.ApiVersionReader = ApiVersionReader.Combine(
                new QueryStringApiVersionReader("api-version"),
                new HeaderApiVersionReader("api-version"),
                new MediaTypeApiVersionReader("ver")
            );
        });

        builder.Services.AddVersionedApiExplorer(options =>
        {
            options.GroupNameFormat = "'v'VVV";
            options.SubstituteApiVersionInUrl = true;
        });

        var app = builder.Build();

        // app.ApplyDatabaseMigrations();

        app.UseSerilogRequestLogging();

        // Configure the HTTP request pipeline.
        if (!app.Environment.IsDevelopment())
        {
            app.UseSwagger();
            app.UseSwaggerUI();
        }

        app.UseExceptionHandler("/errors");

        app.UseCors(options =>
        {
            options.AllowAnyHeader()
                .AllowAnyMethod()
                .AllowCredentials()
                .WithExposedHeaders("Content-Disposition")
                .SetIsOriginAllowed(_ => true);
        });

        app.UseAuthentication();
        app.UseAuthorization();

        app.UseHttpsRedirection();
        app.ConfigureHttpRequestPipeline();

        app.MapControllers();
        app.MapHub<NotificationHub>("/notificationHub");


        MapperConfig.ConfigureMappers();

        app.Run();
    }
}
